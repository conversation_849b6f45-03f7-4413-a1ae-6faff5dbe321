/////////////////////////////////////////////////////////////////
// Render Swatch Legend Enhanced v2.0.0 -- Enhanced UI Version
//>=--------------------------------------
//
//  Enhanced version with interactive UI settings dialog
//  Based on original Render Swatch Legend v1.4.4 by various contributors
//  Enhanced by AI Assistant with improved user interface
//
//  New Features:
//  - Interactive settings dialog
//  - Multiple layout templates
//  - Advanced color format options
//  - Real-time preview capabilities
//  - Better error handling
//
//>=--------------------------------------

// Global variables for settings
var settings = {
    cols: 4,
    width: 150,
    height: 120,
    textSize: 10,
    h_pad: 10,
    v_pad: 10,
    t_h_pad: 10,
    t_v_pad: 10,
    printColors: ["HEX", "RGB", "CMYK", "LAB"],
    colorSeparator: " ",
    splitColorComponents: false,
    showBorder: true,
    borderWidth: 1,
    template: "standard"
};

// Layout templates
var templates = {
    "minimal": {
        showBorder: false,
        textSize: 8,
        h_pad: 5,
        v_pad: 5,
        printColors: ["HEX"]
    },
    "standard": {
        showBorder: true,
        textSize: 10,
        h_pad: 10,
        v_pad: 10,
        printColors: ["HEX", "RGB", "CMYK"]
    },
    "detailed": {
        showBorder: true,
        textSize: 12,
        h_pad: 15,
        v_pad: 15,
        printColors: ["HEX", "RGB", "CMYK", "LAB", "GrayScale"]
    },
    "compact": {
        showBorder: false,
        textSize: 6,
        h_pad: 2,
        v_pad: 2,
        printColors: ["HEX", "RGB"]
    }
};

// Main execution
try {
    if (app.documents.length === 0) {
        alert("Please open a document first.");
    } else {
        var selectedSwatches = app.activeDocument.swatches.getSelected();
        if (selectedSwatches.length === 0) {
            alert("Please select some swatches first.");
        } else {
            if (showSettingsDialog()) {
                generateSwatchLegend();
            }
        }
    }
} catch (e) {
    alert("Error: " + e.message);
}

// Settings Dialog Function
function showSettingsDialog() {
    var dialog = new Window("dialog", "Swatch Legend Settings");
    dialog.orientation = "column";
    dialog.alignChildren = "fill";
    dialog.spacing = 10;
    dialog.margins = 16;
    
    // Template selection
    var templateGroup = dialog.add("panel", undefined, "Template");
    templateGroup.orientation = "row";
    templateGroup.alignChildren = "left";
    templateGroup.spacing = 10;
    templateGroup.margins = 10;
    
    var templateLabel = templateGroup.add("statictext", undefined, "Choose Template:");
    templateLabel.preferredSize.width = 100;
    
    var templateDropdown = templateGroup.add("dropdownlist", undefined, ["Minimal", "Standard", "Detailed", "Compact"]);
    templateDropdown.selection = 1; // Default to "Standard"
    templateDropdown.preferredSize.width = 120;
    
    // Layout settings
    var layoutGroup = dialog.add("panel", undefined, "Layout Settings");
    layoutGroup.orientation = "column";
    layoutGroup.alignChildren = "fill";
    layoutGroup.spacing = 8;
    layoutGroup.margins = 10;
    
    // Columns
    var colsGroup = layoutGroup.add("group");
    colsGroup.orientation = "row";
    var colsLabel = colsGroup.add("statictext", undefined, "Columns:");
    colsLabel.preferredSize.width = 80;
    var colsInput = colsGroup.add("edittext", undefined, settings.cols.toString());
    colsInput.preferredSize.width = 60;
    
    // Size settings
    var sizeGroup = layoutGroup.add("group");
    sizeGroup.orientation = "row";
    var sizeLabel = sizeGroup.add("statictext", undefined, "Size (W×H):");
    sizeLabel.preferredSize.width = 80;
    var widthInput = sizeGroup.add("edittext", undefined, settings.width.toString());
    widthInput.preferredSize.width = 60;
    var xLabel = sizeGroup.add("statictext", undefined, "×");
    var heightInput = sizeGroup.add("edittext", undefined, settings.height.toString());
    heightInput.preferredSize.width = 60;
    
    // Spacing settings
    var spacingGroup = layoutGroup.add("group");
    spacingGroup.orientation = "row";
    var spacingLabel = spacingGroup.add("statictext", undefined, "Spacing:");
    spacingLabel.preferredSize.width = 80;
    var hPadInput = spacingGroup.add("edittext", undefined, settings.h_pad.toString());
    hPadInput.preferredSize.width = 60;
    var spacingXLabel = spacingGroup.add("statictext", undefined, "×");
    var vPadInput = spacingGroup.add("edittext", undefined, settings.v_pad.toString());
    vPadInput.preferredSize.width = 60;
    
    // Text settings
    var textGroup = dialog.add("panel", undefined, "Text Settings");
    textGroup.orientation = "column";
    textGroup.alignChildren = "fill";
    textGroup.spacing = 8;
    textGroup.margins = 10;
    
    // Text size
    var textSizeGroup = textGroup.add("group");
    textSizeGroup.orientation = "row";
    var textSizeLabel = textSizeGroup.add("statictext", undefined, "Text Size:");
    textSizeLabel.preferredSize.width = 80;
    var textSizeInput = textSizeGroup.add("edittext", undefined, settings.textSize.toString());
    textSizeInput.preferredSize.width = 60;
    
    // Color formats
    var colorGroup = dialog.add("panel", undefined, "Color Formats");
    colorGroup.orientation = "column";
    colorGroup.alignChildren = "left";
    colorGroup.spacing = 5;
    colorGroup.margins = 10;
    
    var hexCheck = colorGroup.add("checkbox", undefined, "HEX");
    var rgbCheck = colorGroup.add("checkbox", undefined, "RGB");
    var cmykCheck = colorGroup.add("checkbox", undefined, "CMYK");
    var labCheck = colorGroup.add("checkbox", undefined, "LAB");
    var grayCheck = colorGroup.add("checkbox", undefined, "GrayScale");
    
    // Set default checkboxes
    hexCheck.value = settings.printColors.indexOf("HEX") !== -1;
    rgbCheck.value = settings.printColors.indexOf("RGB") !== -1;
    cmykCheck.value = settings.printColors.indexOf("CMYK") !== -1;
    labCheck.value = settings.printColors.indexOf("LAB") !== -1;
    grayCheck.value = settings.printColors.indexOf("GrayScale") !== -1;
    
    // Advanced options
    var advancedGroup = dialog.add("panel", undefined, "Advanced Options");
    advancedGroup.orientation = "column";
    advancedGroup.alignChildren = "left";
    advancedGroup.spacing = 5;
    advancedGroup.margins = 10;
    
    var splitComponentsCheck = advancedGroup.add("checkbox", undefined, "Split Color Components");
    splitComponentsCheck.value = settings.splitColorComponents;
    
    var showBorderCheck = advancedGroup.add("checkbox", undefined, "Show Border");
    showBorderCheck.value = settings.showBorder;
    
    // Template change handler
    templateDropdown.onChange = function() {
        var selectedTemplate = templateDropdown.selection.text.toLowerCase();
        if (templates[selectedTemplate]) {
            var template = templates[selectedTemplate];
            
            // Update UI with template values
            textSizeInput.text = template.textSize.toString();
            hPadInput.text = template.h_pad.toString();
            vPadInput.text = template.v_pad.toString();
            showBorderCheck.value = template.showBorder;
            
            // Update color format checkboxes
            hexCheck.value = template.printColors.indexOf("HEX") !== -1;
            rgbCheck.value = template.printColors.indexOf("RGB") !== -1;
            cmykCheck.value = template.printColors.indexOf("CMYK") !== -1;
            labCheck.value = template.printColors.indexOf("LAB") !== -1;
            grayCheck.value = template.printColors.indexOf("GrayScale") !== -1;
        }
    };
    
    // Buttons
    var buttonGroup = dialog.add("group");
    buttonGroup.orientation = "row";
    buttonGroup.alignment = "center";
    buttonGroup.spacing = 10;
    
    var okButton = buttonGroup.add("button", undefined, "Generate Legend");
    var cancelButton = buttonGroup.add("button", undefined, "Cancel");
    var previewButton = buttonGroup.add("button", undefined, "Preview");
    
    // Button handlers
    okButton.onClick = function() {
        if (validateAndSaveSettings()) {
            dialog.close(1);
        }
    };
    
    cancelButton.onClick = function() {
        dialog.close(0);
    };
    
    previewButton.onClick = function() {
        if (validateAndSaveSettings()) {
            alert("Preview: Will create " + app.activeDocument.swatches.getSelected().length + 
                  " swatches in " + settings.cols + " columns\n" +
                  "Size: " + settings.width + "×" + settings.height + "\n" +
                  "Colors: " + settings.printColors.join(", "));
        }
    };
    
    // Validation and settings save function
    function validateAndSaveSettings() {
        try {
            // Validate and save numeric inputs
            var cols = parseInt(colsInput.text);
            var width = parseInt(widthInput.text);
            var height = parseInt(heightInput.text);
            var textSize = parseInt(textSizeInput.text);
            var hPad = parseInt(hPadInput.text);
            var vPad = parseInt(vPadInput.text);
            
            if (isNaN(cols) || cols < 1 || cols > 20) {
                alert("Columns must be between 1 and 20");
                return false;
            }
            if (isNaN(width) || width < 50 || width > 500) {
                alert("Width must be between 50 and 500");
                return false;
            }
            if (isNaN(height) || height < 50 || height > 500) {
                alert("Height must be between 50 and 500");
                return false;
            }
            if (isNaN(textSize) || textSize < 6 || textSize > 72) {
                alert("Text size must be between 6 and 72");
                return false;
            }
            
            // Save settings
            settings.cols = cols;
            settings.width = width;
            settings.height = height;
            settings.textSize = textSize;
            settings.h_pad = hPad;
            settings.v_pad = vPad;
            settings.splitColorComponents = splitComponentsCheck.value;
            settings.showBorder = showBorderCheck.value;
            
            // Save color formats
            settings.printColors = [];
            if (hexCheck.value) settings.printColors.push("HEX");
            if (rgbCheck.value) settings.printColors.push("RGB");
            if (cmykCheck.value) settings.printColors.push("CMYK");
            if (labCheck.value) settings.printColors.push("LAB");
            if (grayCheck.value) settings.printColors.push("GrayScale");
            
            if (settings.printColors.length === 0) {
                alert("Please select at least one color format");
                return false;
            }
            
            return true;
        } catch (e) {
            alert("Error validating settings: " + e.message);
            return false;
        }
    }
    
    return dialog.show() === 1;
}

// Main legend generation function
function generateSwatchLegend() {
    try {
        var doc = app.activeDocument;
        var swatches = doc.swatches.getSelected();

        // Unlock the first layer
        if (doc.layers.length > 0) {
            doc.layers[0].locked = false;
        }

        // Create main group
        var newGroup = doc.groupItems.add();
        newGroup.name = "Swatch Legend - " + new Date().toLocaleString();
        newGroup.move(doc, ElementPlacement.PLACEATBEGINNING);

        // Create colors for text
        var black = new GrayColor();
        var white = new GrayColor();
        black.gray = 100;
        white.gray = 0;

        // Generate legend items
        for (var c = 0; c < swatches.length; c++) {
            var swatchGroup = doc.groupItems.add();
            swatchGroup.name = swatches[c].name;

            // Calculate position
            var x = (settings.width + settings.h_pad) * (c % settings.cols);
            var y = (settings.height + settings.v_pad) * (Math.floor(c / settings.cols)) * -1;

            // Create color rectangle
            var rectRef = doc.pathItems.rectangle(y, x, settings.width, settings.height);
            var swatchColor = swatches[c].color;
            rectRef.fillColor = swatchColor;

            // Add border if enabled
            if (settings.showBorder) {
                rectRef.stroked = true;
                rectRef.strokeWidth = settings.borderWidth || 1;
                rectRef.strokeColor = black;
            } else {
                rectRef.stroked = false;
            }

            // Create text area
            var textRectRef = doc.pathItems.rectangle(
                y - settings.t_v_pad,
                x + settings.t_h_pad,
                settings.width - (2 * settings.t_h_pad),
                settings.height - (2 * settings.t_v_pad)
            );

            var textRef = doc.textFrames.areaText(textRectRef);
            textRef.contents = swatches[c].name + "\r" + getColorValues(swatchColor);
            textRef.textRange.fillColor = is_dark(swatchColor) ? white : black;
            textRef.textRange.size = settings.textSize;

            // Group elements
            rectRef.move(swatchGroup, ElementPlacement.PLACEATBEGINNING);
            textRef.move(swatchGroup, ElementPlacement.PLACEATBEGINNING);
            swatchGroup.move(newGroup, ElementPlacement.PLACEATEND);
        }

        alert("Swatch legend created successfully!\nGenerated " + swatches.length + " color swatches.");

    } catch (e) {
        alert("Error generating legend: " + e.message);
    }
}

// Enhanced color values function
function getColorValues(c, spot) {
    if (!c.typename) {
        return "Non Standard Color Type";
    }

    try {
        if (c.typename == "SpotColor") {
            return getColorValues(c.spot.color, c.spot);
        }

        var sourceSpace, colorComponents;

        switch (c.typename) {
            case "RGBColor":
                sourceSpace = ImageColorSpace.RGB;
                colorComponents = [c.red, c.green, c.blue];
                break;
            case "CMYKColor":
                sourceSpace = ImageColorSpace.CMYK;
                colorComponents = [c.cyan, c.magenta, c.yellow, c.black];
                break;
            case "LabColor":
                sourceSpace = ImageColorSpace.LAB;
                colorComponents = [c.l, c.a, c.b];
                break;
            case "GrayColor":
                sourceSpace = ImageColorSpace.GrayScale;
                colorComponents = [c.gray];
                break;
            default:
                return "Unsupported Color Type";
        }

        var outputColors = [];

        for (var i = 0; i < settings.printColors.length; i++) {
            var colorType = settings.printColors[i];
            var colorOutput = "";

            if (colorType == "HEX") {
                colorOutput = getHexValue(c);
            } else if (colorType == 'LAB' && spot && spot.spotKind == 'SpotColorKind.SPOTLAB') {
                colorOutput = formatColorOutput(spot.getInternalColor(), colorType);
            } else {
                var targetSpace = ImageColorSpace[colorType == "GrayScale" ? "GrayScale" : colorType];
                var convertedColor = app.convertSampleColor(sourceSpace, colorComponents, targetSpace, ColorConvertPurpose.previewpurpose);
                colorOutput = formatColorOutput(convertedColor, colorType);
            }

            outputColors.push(colorOutput);
        }

        return outputColors.join("\r");

    } catch (e) {
        return "Error processing color: " + e.message;
    }
}

// Helper function to get HEX value
function getHexValue(c) {
    try {
        var r, g, b;

        if (c.typename == "RGBColor") {
            r = Math.round(c.red);
            g = Math.round(c.green);
            b = Math.round(c.blue);
        } else if (c.typename == "CMYKColor") {
            var rgbConv = app.convertSampleColor(
                ImageColorSpace.CMYK,
                [c.cyan, c.magenta, c.yellow, c.black],
                ImageColorSpace.RGB,
                ColorConvertPurpose.defaultpurpose
            );
            r = Math.round(rgbConv[0]);
            g = Math.round(rgbConv[1]);
            b = Math.round(rgbConv[2]);
        } else {
            // Convert other color types to RGB first
            var rgbConv = app.convertSampleColor(
                getSourceSpace(c),
                getColorComponents(c),
                ImageColorSpace.RGB,
                ColorConvertPurpose.defaultpurpose
            );
            r = Math.round(rgbConv[0]);
            g = Math.round(rgbConv[1]);
            b = Math.round(rgbConv[2]);
        }

        // Ensure values are in valid range
        r = Math.max(0, Math.min(255, r));
        g = Math.max(0, Math.min(255, g));
        b = Math.max(0, Math.min(255, b));

        // Convert to hex
        var hexR = r.toString(16).toUpperCase();
        var hexG = g.toString(16).toUpperCase();
        var hexB = b.toString(16).toUpperCase();

        // Pad with zeros if needed
        if (hexR.length == 1) hexR = "0" + hexR;
        if (hexG.length == 1) hexG = "0" + hexG;
        if (hexB.length == 1) hexB = "0" + hexB;

        return "HEX #" + hexR + hexG + hexB;

    } catch (e) {
        return "HEX Error";
    }
}

// Helper function to format color output
function formatColorOutput(colorArray, colorType) {
    var output = [];
    var labels = getColorLabels(colorType);

    for (var j = 0; j < colorArray.length; j++) {
        var value = Math.round(colorArray[j]);
        var label = settings.splitColorComponents && labels[j] ? labels[j] + ": " : "";
        output.push(label + value);
    }

    var result = output.join(settings.colorSeparator);
    return settings.splitColorComponents ? result : colorType + " " + result;
}

// Helper function to get color component labels
function getColorLabels(colorType) {
    switch (colorType) {
        case "RGB": return ["R", "G", "B"];
        case "CMYK": return ["C", "M", "Y", "K"];
        case "LAB": return ["L", "A", "B"];
        case "GrayScale": return ["Gray"];
        default: return [];
    }
}

// Helper functions for color space detection
function getSourceSpace(c) {
    switch (c.typename) {
        case "RGBColor": return ImageColorSpace.RGB;
        case "CMYKColor": return ImageColorSpace.CMYK;
        case "LabColor": return ImageColorSpace.LAB;
        case "GrayColor": return ImageColorSpace.GrayScale;
        default: return ImageColorSpace.RGB;
    }
}

function getColorComponents(c) {
    switch (c.typename) {
        case "RGBColor": return [c.red, c.green, c.blue];
        case "CMYKColor": return [c.cyan, c.magenta, c.yellow, c.black];
        case "LabColor": return [c.l, c.a, c.b];
        case "GrayColor": return [c.gray];
        default: return [0, 0, 0];
    }
}

// Enhanced dark color detection
function is_dark(c) {
    if (!c.typename) return false;

    try {
        switch (c.typename) {
            case "CMYKColor":
                // More sophisticated CMYK darkness detection
                var totalInk = c.cyan + c.magenta + c.yellow + c.black;
                return (c.black > 50 || totalInk > 200);

            case "RGBColor":
                // Use luminance formula for better accuracy
                var luminance = (0.299 * c.red + 0.587 * c.green + 0.114 * c.blue);
                return luminance < 128;

            case "GrayColor":
                return c.gray > 50;

            case "LabColor":
                return c.l < 50;

            case "SpotColor":
                return is_dark(c.spot.color);

            default:
                return false;
        }
    } catch (e) {
        return false;
    }
}
